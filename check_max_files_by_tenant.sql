-- Script to check maximum files for each tenant
-- Usage: psql -d atd_jobs_db -f check_max_files_by_tenant.sql

\echo '=== ATD Jobs File Analysis by Tenant ==='
\echo ''

-- Summary by tenant
\echo '1. Summary by Tenant (Total and Max Files):'
SELECT 
    tenant_id,
    COUNT(*) as total_jobs,
    SUM(jsonb_array_length(files)) as total_files,
    MAX(jsonb_array_length(files)) as max_files_per_job,
    MIN(jsonb_array_length(files)) as min_files_per_job,
    AVG(jsonb_array_length(files))::numeric(10,2) as avg_files_per_job
FROM public.atd_jobs 
GROUP BY tenant_id 
ORDER BY max_files_per_job DESC, total_files DESC;

\echo ''
\echo '2. Top 10 Jobs with Most Files:'
SELECT 
    tenant_id,
    profile_id,
    jsonb_array_length(files) as file_count,
    status,
    timestamp::date as job_date
FROM public.atd_jobs 
ORDER BY jsonb_array_length(files) DESC
LIMIT 10;

\echo ''
\echo '3. Detailed breakdown by tenant and profile:'
SELECT 
    tenant_id,
    profile_id,
    jsonb_array_length(files) as file_count,
    status,
    last_updated::date as last_update_date
FROM public.atd_jobs 
WHERE jsonb_array_length(files) > 0
ORDER BY tenant_id, jsonb_array_length(files) DESC;

\echo ''
\echo '4. File count distribution:'
SELECT 
    CASE 
        WHEN jsonb_array_length(files) = 0 THEN '0 files'
        WHEN jsonb_array_length(files) BETWEEN 1 AND 10 THEN '1-10 files'
        WHEN jsonb_array_length(files) BETWEEN 11 AND 100 THEN '11-100 files'
        WHEN jsonb_array_length(files) BETWEEN 101 AND 1000 THEN '101-1000 files'
        WHEN jsonb_array_length(files) BETWEEN 1001 AND 10000 THEN '1001-10000 files'
        ELSE '10000+ files'
    END as file_range,
    COUNT(*) as job_count
FROM public.atd_jobs 
GROUP BY 
    CASE 
        WHEN jsonb_array_length(files) = 0 THEN '0 files'
        WHEN jsonb_array_length(files) BETWEEN 1 AND 10 THEN '1-10 files'
        WHEN jsonb_array_length(files) BETWEEN 11 AND 100 THEN '11-100 files'
        WHEN jsonb_array_length(files) BETWEEN 101 AND 1000 THEN '101-1000 files'
        WHEN jsonb_array_length(files) BETWEEN 1001 AND 10000 THEN '1001-10000 files'
        ELSE '10000+ files'
    END
ORDER BY
    CASE
        WHEN CASE
            WHEN jsonb_array_length(files) = 0 THEN '0 files'
            WHEN jsonb_array_length(files) BETWEEN 1 AND 10 THEN '1-10 files'
            WHEN jsonb_array_length(files) BETWEEN 11 AND 100 THEN '11-100 files'
            WHEN jsonb_array_length(files) BETWEEN 101 AND 1000 THEN '101-1000 files'
            WHEN jsonb_array_length(files) BETWEEN 1001 AND 10000 THEN '1001-10000 files'
            ELSE '10000+ files'
        END = '0 files' THEN 1
        WHEN CASE
            WHEN jsonb_array_length(files) = 0 THEN '0 files'
            WHEN jsonb_array_length(files) BETWEEN 1 AND 10 THEN '1-10 files'
            WHEN jsonb_array_length(files) BETWEEN 11 AND 100 THEN '11-100 files'
            WHEN jsonb_array_length(files) BETWEEN 101 AND 1000 THEN '101-1000 files'
            WHEN jsonb_array_length(files) BETWEEN 1001 AND 10000 THEN '1001-10000 files'
            ELSE '10000+ files'
        END = '1-10 files' THEN 2
        WHEN CASE
            WHEN jsonb_array_length(files) = 0 THEN '0 files'
            WHEN jsonb_array_length(files) BETWEEN 1 AND 10 THEN '1-10 files'
            WHEN jsonb_array_length(files) BETWEEN 11 AND 100 THEN '11-100 files'
            WHEN jsonb_array_length(files) BETWEEN 101 AND 1000 THEN '101-1000 files'
            WHEN jsonb_array_length(files) BETWEEN 1001 AND 10000 THEN '1001-10000 files'
            ELSE '10000+ files'
        END = '11-100 files' THEN 3
        WHEN CASE
            WHEN jsonb_array_length(files) = 0 THEN '0 files'
            WHEN jsonb_array_length(files) BETWEEN 1 AND 10 THEN '1-10 files'
            WHEN jsonb_array_length(files) BETWEEN 11 AND 100 THEN '11-100 files'
            WHEN jsonb_array_length(files) BETWEEN 101 AND 1000 THEN '101-1000 files'
            WHEN jsonb_array_length(files) BETWEEN 1001 AND 10000 THEN '1001-10000 files'
            ELSE '10000+ files'
        END = '101-1000 files' THEN 4
        WHEN CASE
            WHEN jsonb_array_length(files) = 0 THEN '0 files'
            WHEN jsonb_array_length(files) BETWEEN 1 AND 10 THEN '1-10 files'
            WHEN jsonb_array_length(files) BETWEEN 11 AND 100 THEN '11-100 files'
            WHEN jsonb_array_length(files) BETWEEN 101 AND 1000 THEN '101-1000 files'
            WHEN jsonb_array_length(files) BETWEEN 1001 AND 10000 THEN '1001-10000 files'
            ELSE '10000+ files'
        END = '1001-10000 files' THEN 5
        ELSE 6
    END;

\echo ''
\echo '=== Analysis Complete ==='

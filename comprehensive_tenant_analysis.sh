#!/bin/bash

# Comprehensive script to analyze tenant files from both database and original file
# Usage: ./comprehensive_tenant_analysis.sh [--source db|file|both] [--db database_name] [--file file_path] [--tenant tenant_name]

# Default values
SOURCE="both"
DB_NAME="atd_jobs_db"
FILE_PATH="atd_jobs.pgsql"
SPECIFIC_TENANT=""

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
CYAN='\033[0;36m'
NC='\033[0m'

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --source)
            SOURCE="$2"
            shift 2
            ;;
        --db)
            DB_NAME="$2"
            shift 2
            ;;
        --file)
            FILE_PATH="$2"
            shift 2
            ;;
        --tenant)
            SPECIFIC_TENANT="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --source db|file|both    Source to analyze (default: both)"
            echo "  --db DATABASE_NAME       Database name (default: atd_jobs_db)"
            echo "  --file FILE_PATH         Path to .pgsql file (default: atd_jobs.pgsql)"
            echo "  --tenant TENANT_NAME     Analyze specific tenant only"
            echo "  -h, --help               Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo -e "${BLUE}=== Comprehensive ATD Jobs File Analysis ===${NC}"
echo "Date: $(date)"
echo "Source: $SOURCE"
echo ""

# Function to analyze database
analyze_database() {
    echo -e "${CYAN}=== DATABASE ANALYSIS ===${NC}"
    echo "Database: $DB_NAME"
    echo ""
    
    # Check if database exists
    if ! sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw $DB_NAME; then
        echo -e "${RED}Error: Database '$DB_NAME' does not exist${NC}"
        return 1
    fi
    
    if [ ! -z "$SPECIFIC_TENANT" ]; then
        echo -e "${YELLOW}Tenant: $SPECIFIC_TENANT${NC}"
        sudo -u postgres psql -d $DB_NAME -c "
        SELECT 
            tenant_id,
            profile_id,
            jsonb_array_length(files) as file_count,
            status,
            timestamp::date as job_date
        FROM public.atd_jobs 
        WHERE tenant_id = '$SPECIFIC_TENANT'
        ORDER BY jsonb_array_length(files) DESC;"
        
        echo ""
        sudo -u postgres psql -d $DB_NAME -c "
        SELECT 
            'Total Jobs' as metric,
            COUNT(*)::text as value
        FROM public.atd_jobs 
        WHERE tenant_id = '$SPECIFIC_TENANT'
        UNION ALL
        SELECT 
            'Total Files',
            SUM(jsonb_array_length(files))::text
        FROM public.atd_jobs 
        WHERE tenant_id = '$SPECIFIC_TENANT'
        UNION ALL
        SELECT 
            'Max Files',
            MAX(jsonb_array_length(files))::text
        FROM public.atd_jobs 
        WHERE tenant_id = '$SPECIFIC_TENANT';"
    else
        echo -e "${GREEN}Summary by Tenant:${NC}"
        sudo -u postgres psql -d $DB_NAME -c "
        SELECT 
            tenant_id,
            COUNT(*) as total_jobs,
            SUM(jsonb_array_length(files)) as total_files,
            MAX(jsonb_array_length(files)) as max_files
        FROM public.atd_jobs 
        GROUP BY tenant_id 
        ORDER BY max_files DESC, total_files DESC;"
        
        echo ""
        echo -e "${GREEN}Winner from Database:${NC}"
        sudo -u postgres psql -d $DB_NAME -c "
        SELECT 
            tenant_id,
            MAX(jsonb_array_length(files)) as max_files
        FROM public.atd_jobs 
        GROUP BY tenant_id 
        ORDER BY max_files DESC 
        LIMIT 1;"
    fi
}

# Function to analyze original file
analyze_file() {
    echo -e "${CYAN}=== ORIGINAL FILE ANALYSIS ===${NC}"
    echo "File: $FILE_PATH"
    echo ""
    
    if [ ! -f "$FILE_PATH" ]; then
        echo -e "${RED}Error: File '$FILE_PATH' does not exist${NC}"
        return 1
    fi
    
    echo -e "${GREEN}File Statistics:${NC}"
    echo "File size: $(du -h $FILE_PATH | cut -f1)"
    echo "Total lines: $(wc -l < $FILE_PATH)"
    echo ""
    
    if [ ! -z "$SPECIFIC_TENANT" ]; then
        echo -e "${YELLOW}Searching for tenant: $SPECIFIC_TENANT${NC}"
        tenant_lines=$(grep "^$SPECIFIC_TENANT" $FILE_PATH)
        if [ ! -z "$tenant_lines" ]; then
            echo "$tenant_lines" | while read line; do
                tenant=$(echo "$line" | cut -f1)
                profile=$(echo "$line" | cut -f2)
                files_json=$(echo "$line" | cut -f3)
                file_count=$(echo "$files_json" | tr ',' '\n' | grep -c '"filename"')
                echo "Tenant: $tenant, Profile: $profile, Files: $file_count"
            done
        else
            echo "No data found for tenant: $SPECIFIC_TENANT"
        fi
    else
        echo -e "${GREEN}Analyzing all tenants in file:${NC}"
        echo "Tenant | Profile | File Count"
        echo "-------|---------|----------"
        
        # Extract tenant data and count files
        awk -F'\t' 'NR>=46 && NR<=92 && NF>=3 {
            tenant = $1
            profile = $2
            files = $3
            
            # Count filename occurrences
            cmd = "echo \"" files "\" | tr \",\" \"\\n\" | grep -c \"\\\"filename\\\"\""
            cmd | getline file_count
            close(cmd)
            
            printf "%-25s | %-20s | %s\n", tenant, profile, file_count
        }' $FILE_PATH
        
        echo ""
        echo -e "${GREEN}Winner from Original File:${NC}"
        
        # Find the tenant with maximum files
        max_files=0
        max_tenant=""
        max_profile=""
        
        while IFS=$'\t' read -r tenant profile files_json rest; do
            if [[ "$tenant" != "" && "$files_json" != "[]" ]]; then
                file_count=$(echo "$files_json" | tr ',' '\n' | grep -c '"filename"')
                if [ "$file_count" -gt "$max_files" ]; then
                    max_files=$file_count
                    max_tenant=$tenant
                    max_profile=$profile
                fi
            fi
        done < <(sed -n '46,92p' $FILE_PATH)
        
        echo "🏆 $max_tenant ($max_profile): $max_files files"
    fi
}

# Main execution
case $SOURCE in
    "db")
        analyze_database
        ;;
    "file")
        analyze_file
        ;;
    "both")
        analyze_database
        echo ""
        echo "----------------------------------------"
        echo ""
        analyze_file
        ;;
    *)
        echo -e "${RED}Error: Invalid source '$SOURCE'. Use 'db', 'file', or 'both'${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${BLUE}=== Analysis Complete ===${NC}"
echo ""
echo "Quick commands:"
echo "  Database only:     $0 --source db"
echo "  File only:         $0 --source file"
echo "  Specific tenant:   $0 --tenant nykaa_default"
echo "  Custom file:       $0 --file /path/to/file.pgsql"

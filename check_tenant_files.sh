#!/bin/bash

# Script to check maximum files for each tenant in ATD Jobs database
# Usage: ./check_tenant_files.sh [database_name] [specific_tenant]

# Configuration
DB_NAME=${1:-"atd_jobs_db"}
SPECIFIC_TENANT=$2
POSTGRES_USER="postgres"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== ATD Jobs File Analysis ===${NC}"
echo "Database: $DB_NAME"
echo "Date: $(date)"
echo ""

# Function to run SQL query
run_query() {
    local query="$1"
    sudo -u $POSTGRES_USER psql -d $DB_NAME -t -c "$query"
}

# Function to run SQL query with headers
run_query_with_headers() {
    local query="$1"
    sudo -u $POSTGRES_USER psql -d $DB_NAME -c "$query"
}

# Check if database exists
if ! sudo -u $POSTGRES_USER psql -lqt | cut -d \| -f 1 | grep -qw $DB_NAME; then
    echo -e "${RED}Error: Database '$DB_NAME' does not exist${NC}"
    exit 1
fi

# If specific tenant is provided
if [ ! -z "$SPECIFIC_TENANT" ]; then
    echo -e "${YELLOW}=== Analysis for Tenant: $SPECIFIC_TENANT ===${NC}"
    
    query="SELECT 
        tenant_id,
        profile_id,
        jsonb_array_length(files) as file_count,
        status,
        timestamp::date as job_date,
        last_updated::date as last_update
    FROM public.atd_jobs 
    WHERE tenant_id = '$SPECIFIC_TENANT'
    ORDER BY jsonb_array_length(files) DESC;"
    
    run_query_with_headers "$query"
    
    # Get summary for this tenant
    echo ""
    echo -e "${YELLOW}Summary for $SPECIFIC_TENANT:${NC}"
    summary_query="SELECT 
        COUNT(*) as total_jobs,
        SUM(jsonb_array_length(files)) as total_files,
        MAX(jsonb_array_length(files)) as max_files,
        MIN(jsonb_array_length(files)) as min_files,
        AVG(jsonb_array_length(files))::numeric(10,2) as avg_files
    FROM public.atd_jobs 
    WHERE tenant_id = '$SPECIFIC_TENANT';"
    
    run_query_with_headers "$summary_query"
    exit 0
fi

# Overall analysis
echo -e "${GREEN}1. Summary by Tenant:${NC}"
query1="SELECT 
    tenant_id,
    COUNT(*) as total_jobs,
    SUM(jsonb_array_length(files)) as total_files,
    MAX(jsonb_array_length(files)) as max_files_per_job,
    AVG(jsonb_array_length(files))::numeric(10,2) as avg_files_per_job
FROM public.atd_jobs 
GROUP BY tenant_id 
ORDER BY max_files_per_job DESC, total_files DESC;"

run_query_with_headers "$query1"

echo ""
echo -e "${GREEN}2. Top 5 Jobs with Most Files:${NC}"
query2="SELECT 
    tenant_id,
    profile_id,
    jsonb_array_length(files) as file_count,
    status,
    timestamp::date as job_date
FROM public.atd_jobs 
ORDER BY jsonb_array_length(files) DESC
LIMIT 5;"

run_query_with_headers "$query2"

echo ""
echo -e "${GREEN}3. Tenant with Maximum Files:${NC}"
query3="SELECT 
    tenant_id,
    MAX(jsonb_array_length(files)) as max_files
FROM public.atd_jobs 
GROUP BY tenant_id 
ORDER BY max_files DESC 
LIMIT 1;"

result=$(run_query "$query3")
if [ ! -z "$result" ]; then
    tenant=$(echo "$result" | awk '{print $1}')
    max_files=$(echo "$result" | awk '{print $3}')
    echo -e "${YELLOW}Winner: $tenant with $max_files files${NC}"
else
    echo -e "${RED}No data found${NC}"
fi

echo ""
echo -e "${GREEN}4. File Count Distribution:${NC}"
query4="SELECT 
    CASE 
        WHEN jsonb_array_length(files) = 0 THEN '0 files'
        WHEN jsonb_array_length(files) BETWEEN 1 AND 10 THEN '1-10 files'
        WHEN jsonb_array_length(files) BETWEEN 11 AND 100 THEN '11-100 files'
        WHEN jsonb_array_length(files) BETWEEN 101 AND 1000 THEN '101-1000 files'
        WHEN jsonb_array_length(files) BETWEEN 1001 AND 10000 THEN '1001-10000 files'
        ELSE '10000+ files'
    END as file_range,
    COUNT(*) as job_count
FROM public.atd_jobs 
GROUP BY 
    CASE 
        WHEN jsonb_array_length(files) = 0 THEN '0 files'
        WHEN jsonb_array_length(files) BETWEEN 1 AND 10 THEN '1-10 files'
        WHEN jsonb_array_length(files) BETWEEN 11 AND 100 THEN '11-100 files'
        WHEN jsonb_array_length(files) BETWEEN 101 AND 1000 THEN '101-1000 files'
        WHEN jsonb_array_length(files) BETWEEN 1001 AND 10000 THEN '1001-10000 files'
        ELSE '10000+ files'
    END
ORDER BY 
    CASE 
        WHEN file_range = '0 files' THEN 1
        WHEN file_range = '1-10 files' THEN 2
        WHEN file_range = '11-100 files' THEN 3
        WHEN file_range = '101-1000 files' THEN 4
        WHEN file_range = '1001-10000 files' THEN 5
        ELSE 6
    END;"

run_query_with_headers "$query4"

echo ""
echo -e "${BLUE}=== Analysis Complete ===${NC}"
echo ""
echo "Usage examples:"
echo "  $0                          # Analyze all tenants"
echo "  $0 atd_jobs_db              # Specify database name"
echo "  $0 atd_jobs_db nykaa_default # Analyze specific tenant"

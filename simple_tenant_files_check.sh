#!/bin/bash

# Simple script to check maximum files for each tenant
# Usage: ./simple_tenant_files_check.sh [database_name] [tenant_name]

DB_NAME=${1:-"atd_jobs_db"}
SPECIFIC_TENANT=$2

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}=== ATD Jobs File Analysis ===${NC}"
echo "Database: $DB_NAME"
echo "Date: $(date)"
echo ""

# Function to run SQL query
run_query() {
    sudo -u postgres psql -d $DB_NAME -c "$1"
}

# Check if database exists
if ! sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw $DB_NAME; then
    echo -e "${RED}Error: Database '$DB_NAME' does not exist${NC}"
    exit 1
fi

if [ ! -z "$SPECIFIC_TENANT" ]; then
    echo -e "${YELLOW}=== Analysis for Tenant: $SPECIFIC_TENANT ===${NC}"
    
    # Check tenant data
    run_query "SELECT 
        tenant_id,
        profile_id,
        jsonb_array_length(files) as file_count,
        status,
        timestamp::date as job_date
    FROM public.atd_jobs 
    WHERE tenant_id = '$SPECIFIC_TENANT'
    ORDER BY jsonb_array_length(files) DESC;"
    
    echo ""
    echo -e "${YELLOW}Summary for $SPECIFIC_TENANT:${NC}"
    run_query "SELECT 
        COUNT(*) as total_jobs,
        SUM(jsonb_array_length(files)) as total_files,
        MAX(jsonb_array_length(files)) as max_files
    FROM public.atd_jobs 
    WHERE tenant_id = '$SPECIFIC_TENANT';"
    
else
    echo -e "${GREEN}1. Summary by Tenant:${NC}"
    run_query "SELECT 
        tenant_id,
        COUNT(*) as total_jobs,
        SUM(jsonb_array_length(files)) as total_files,
        MAX(jsonb_array_length(files)) as max_files
    FROM public.atd_jobs 
    GROUP BY tenant_id 
    ORDER BY max_files DESC, total_files DESC;"
    
    echo ""
    echo -e "${GREEN}2. Top 5 Jobs with Most Files:${NC}"
    run_query "SELECT 
        tenant_id,
        profile_id,
        jsonb_array_length(files) as file_count,
        status
    FROM public.atd_jobs 
    ORDER BY jsonb_array_length(files) DESC
    LIMIT 5;"
    
    echo ""
    echo -e "${GREEN}3. Winner (Tenant with Max Files):${NC}"
    run_query "SELECT 
        tenant_id,
        MAX(jsonb_array_length(files)) as max_files
    FROM public.atd_jobs 
    GROUP BY tenant_id 
    ORDER BY max_files DESC 
    LIMIT 1;"
fi

echo ""
echo -e "${BLUE}=== Additional Information ===${NC}"
echo "Note: The current database only contains sample data."
echo "The original atd_jobs.pgsql file contains nykaa_default with 12,809 files."
echo ""
echo "To check the original file directly:"
echo "  sed -n '56p' atd_jobs.pgsql | cut -f3 | tr ',' '\n' | grep -c '\"filename\"'"
echo ""
echo "Usage:"
echo "  $0                    # Analyze all tenants in database"
echo "  $0 db_name            # Specify database name"
echo "  $0 db_name tenant     # Analyze specific tenant"

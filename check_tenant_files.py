#!/usr/bin/env python3
"""
Script to check maximum files for each tenant in ATD Jobs database
Usage: python3 check_tenant_files.py [--tenant TENANT_NAME] [--db DATABASE_NAME]
"""

import psycopg2
import argparse
import sys
from tabulate import tabulate
import json

class TenantFileAnalyzer:
    def __init__(self, db_name="atd_jobs_db", host="localhost", user="postgres"):
        self.db_name = db_name
        self.host = host
        self.user = user
        self.conn = None
        
    def connect(self):
        """Connect to PostgreSQL database"""
        try:
            self.conn = psycopg2.connect(
                host=self.host,
                database=self.db_name,
                user=self.user
            )
            return True
        except psycopg2.Error as e:
            print(f"Error connecting to database: {e}")
            return False
    
    def execute_query(self, query, params=None):
        """Execute a query and return results"""
        try:
            with self.conn.cursor() as cur:
                cur.execute(query, params)
                return cur.fetchall(), [desc[0] for desc in cur.description]
        except psycopg2.Error as e:
            print(f"Error executing query: {e}")
            return None, None
    
    def analyze_all_tenants(self):
        """Analyze all tenants"""
        print("=== ATD Jobs File Analysis ===\n")
        
        # Summary by tenant
        query = """
        SELECT 
            tenant_id,
            COUNT(*) as total_jobs,
            SUM(jsonb_array_length(files)) as total_files,
            MAX(jsonb_array_length(files)) as max_files_per_job,
            MIN(jsonb_array_length(files)) as min_files_per_job,
            AVG(jsonb_array_length(files))::numeric(10,2) as avg_files_per_job
        FROM public.atd_jobs 
        GROUP BY tenant_id 
        ORDER BY max_files_per_job DESC, total_files DESC;
        """
        
        results, headers = self.execute_query(query)
        if results:
            print("1. Summary by Tenant:")
            print(tabulate(results, headers=headers, tablefmt="grid"))
            print()
        
        # Top jobs with most files
        query2 = """
        SELECT 
            tenant_id,
            profile_id,
            jsonb_array_length(files) as file_count,
            status,
            timestamp::date as job_date
        FROM public.atd_jobs 
        ORDER BY jsonb_array_length(files) DESC
        LIMIT 10;
        """
        
        results2, headers2 = self.execute_query(query2)
        if results2:
            print("2. Top 10 Jobs with Most Files:")
            print(tabulate(results2, headers=headers2, tablefmt="grid"))
            print()
        
        # File count distribution
        query3 = """
        SELECT 
            CASE 
                WHEN jsonb_array_length(files) = 0 THEN '0 files'
                WHEN jsonb_array_length(files) BETWEEN 1 AND 10 THEN '1-10 files'
                WHEN jsonb_array_length(files) BETWEEN 11 AND 100 THEN '11-100 files'
                WHEN jsonb_array_length(files) BETWEEN 101 AND 1000 THEN '101-1000 files'
                WHEN jsonb_array_length(files) BETWEEN 1001 AND 10000 THEN '1001-10000 files'
                ELSE '10000+ files'
            END as file_range,
            COUNT(*) as job_count
        FROM public.atd_jobs 
        GROUP BY 
            CASE 
                WHEN jsonb_array_length(files) = 0 THEN '0 files'
                WHEN jsonb_array_length(files) BETWEEN 1 AND 10 THEN '1-10 files'
                WHEN jsonb_array_length(files) BETWEEN 11 AND 100 THEN '11-100 files'
                WHEN jsonb_array_length(files) BETWEEN 101 AND 1000 THEN '101-1000 files'
                WHEN jsonb_array_length(files) BETWEEN 1001 AND 10000 THEN '1001-10000 files'
                ELSE '10000+ files'
            END
        ORDER BY 
            CASE 
                WHEN file_range = '0 files' THEN 1
                WHEN file_range = '1-10 files' THEN 2
                WHEN file_range = '11-100 files' THEN 3
                WHEN file_range = '101-1000 files' THEN 4
                WHEN file_range = '1001-10000 files' THEN 5
                ELSE 6
            END;
        """
        
        results3, headers3 = self.execute_query(query3)
        if results3:
            print("3. File Count Distribution:")
            print(tabulate(results3, headers=headers3, tablefmt="grid"))
            print()
    
    def analyze_specific_tenant(self, tenant_name):
        """Analyze a specific tenant"""
        print(f"=== Analysis for Tenant: {tenant_name} ===\n")
        
        # Check if tenant exists
        check_query = "SELECT COUNT(*) FROM public.atd_jobs WHERE tenant_id = %s;"
        results, _ = self.execute_query(check_query, (tenant_name,))
        
        if not results or results[0][0] == 0:
            print(f"No data found for tenant: {tenant_name}")
            return
        
        # Detailed analysis for specific tenant
        query = """
        SELECT 
            tenant_id,
            profile_id,
            jsonb_array_length(files) as file_count,
            status,
            timestamp::date as job_date,
            last_updated::date as last_update
        FROM public.atd_jobs 
        WHERE tenant_id = %s
        ORDER BY jsonb_array_length(files) DESC;
        """
        
        results, headers = self.execute_query(query, (tenant_name,))
        if results:
            print("Jobs for this tenant:")
            print(tabulate(results, headers=headers, tablefmt="grid"))
            print()
        
        # Summary for this tenant
        summary_query = """
        SELECT 
            COUNT(*) as total_jobs,
            SUM(jsonb_array_length(files)) as total_files,
            MAX(jsonb_array_length(files)) as max_files,
            MIN(jsonb_array_length(files)) as min_files,
            AVG(jsonb_array_length(files))::numeric(10,2) as avg_files
        FROM public.atd_jobs 
        WHERE tenant_id = %s;
        """
        
        summary_results, summary_headers = self.execute_query(summary_query, (tenant_name,))
        if summary_results:
            print(f"Summary for {tenant_name}:")
            print(tabulate(summary_results, headers=summary_headers, tablefmt="grid"))
            print()
    
    def get_max_files_tenant(self):
        """Get the tenant with maximum files"""
        query = """
        SELECT 
            tenant_id,
            MAX(jsonb_array_length(files)) as max_files
        FROM public.atd_jobs 
        GROUP BY tenant_id 
        ORDER BY max_files DESC 
        LIMIT 1;
        """
        
        results, _ = self.execute_query(query)
        if results:
            tenant, max_files = results[0]
            print(f"🏆 Tenant with maximum files: {tenant} ({max_files:,} files)")
            return tenant, max_files
        return None, 0
    
    def close(self):
        """Close database connection"""
        if self.conn:
            self.conn.close()

def main():
    parser = argparse.ArgumentParser(description="Analyze ATD Jobs file counts by tenant")
    parser.add_argument("--tenant", "-t", help="Specific tenant to analyze")
    parser.add_argument("--db", "-d", default="atd_jobs_db", help="Database name")
    parser.add_argument("--max-only", "-m", action="store_true", help="Show only the tenant with max files")
    
    args = parser.parse_args()
    
    analyzer = TenantFileAnalyzer(db_name=args.db)
    
    if not analyzer.connect():
        sys.exit(1)
    
    try:
        if args.max_only:
            analyzer.get_max_files_tenant()
        elif args.tenant:
            analyzer.analyze_specific_tenant(args.tenant)
        else:
            analyzer.analyze_all_tenants()
            print("\n" + "="*50)
            analyzer.get_max_files_tenant()
            
    finally:
        analyzer.close()

if __name__ == "__main__":
    main()
